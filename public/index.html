<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Socket.IO Test Client</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            font-weight: bold;
        }
        .connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        input, button {
            padding: 10px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background-color: #007bff;
            color: white;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        #messages {
            border: 1px solid #ddd;
            height: 300px;
            overflow-y: auto;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 5px;
            margin-top: 10px;
        }
        .message {
            margin-bottom: 10px;
            padding: 5px;
            border-radius: 3px;
        }
        .sent {
            background-color: #e3f2fd;
            text-align: right;
        }
        .received {
            background-color: #f1f8e9;
        }
        .system {
            background-color: #d1ecf1;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Socket.IO Test Client</h1>
        
        <div id="status" class="status disconnected">
            Status: Disconnected
        </div>
        
        <div>
            <h3>Connection Parameters</h3>
            <input type="text" id="usernameInput" placeholder="Username" style="width: 30%;">
            <input type="text" id="userIdInput" placeholder="User ID" style="width: 30%;">
            <input type="text" id="roomInput" placeholder="Room (optional)" style="width: 30%;">
            <br>
            <button onclick="connectWithParams()" id="connectBtn">Connect with Parameters</button>
            <button onclick="disconnect()" id="disconnectBtn">Disconnect</button>
        </div>
        
        <div>
            <input type="text" id="messageInput" placeholder="Enter your message..." style="width: 70%;">
            <button onclick="sendMessage()">Send Message</button>
        </div>
        
        <div id="messages"></div>
    </div>

    <script src="/socket.io/socket.io.js"></script>
    <script>
        let socket = null;
        const statusDiv = document.getElementById('status');
        const messagesDiv = document.getElementById('messages');
        const messageInput = document.getElementById('messageInput');
        const usernameInput = document.getElementById('usernameInput');
        const userIdInput = document.getElementById('userIdInput');
        const roomInput = document.getElementById('roomInput');
        const connectBtn = document.getElementById('connectBtn');
        const disconnectBtn = document.getElementById('disconnectBtn');

        // Initialize with basic connection
        socket = io();
        setupSocketEvents();

        // Connection events
        function setupSocketEvents() {
            socket.on('connect', () => {
                statusDiv.textContent = `Status: Connected (ID: ${socket.id})`;
                statusDiv.className = 'status connected';
                addMessage('Connected to server', 'system');
                connectBtn.disabled = true;
                disconnectBtn.disabled = false;
            });

            socket.on('disconnect', () => {
                statusDiv.textContent = 'Status: Disconnected';
                statusDiv.className = 'status disconnected';
                addMessage('Disconnected from server', 'system');
                connectBtn.disabled = false;
                disconnectBtn.disabled = true;
            });

            // Message events
            socket.on('response', (message) => {
                addMessage(`Server: ${message}`, 'received');
            });
        }

        // Functions
        function connectWithParams() {
            if (socket && socket.connected) {
                socket.disconnect();
            }

            const username = usernameInput.value.trim();
            const userId = userIdInput.value.trim();
            const room = roomInput.value.trim();

            const connectionParams = {};
            if (username) connectionParams.username = username;
            if (userId) connectionParams.userId = userId;
            if (room) connectionParams.room = room;

            // Connect with parameters using query
            socket = io({
                query: connectionParams
            });

            setupSocketEvents();
            addMessage(`Connecting with params: ${JSON.stringify(connectionParams)}`, 'system');
        }

        function disconnect() {
            if (socket) {
                socket.disconnect();
            }
        }
        function sendMessage() {
            const message = messageInput.value.trim();
            if (message && socket && socket.connected) {
                socket.emit('message', message);
                addMessage(`You: ${message}`, 'sent');
                messageInput.value = '';
            }
        }

        function addMessage(text, type) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            messageDiv.textContent = `[${new Date().toLocaleTimeString()}] ${text}`;
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        // Allow Enter key to send message
        messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });
    </script>
</body>
</html>
