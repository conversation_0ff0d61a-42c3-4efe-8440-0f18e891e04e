#!/bin/bash

# HSM WebSocket Deployment Script

set -e  # Exit on any error

echo "🚀 Starting HSM WebSocket deployment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if .env file exists
if [ ! -f ".env" ]; then
    print_error ".env file not found!"
    print_warning "Please create a .env file with your configuration"
    print_warning "You can use .env.production as a template"
    exit 1
fi

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi

print_status "Building Docker image..."
docker build -t hsm-websocket:latest .

print_status "Stopping existing container (if any)..."
docker stop hsm-websocket-container 2>/dev/null || true
docker rm hsm-websocket-container 2>/dev/null || true

print_status "Starting new container..."
docker run -d \
    --name hsm-websocket-container \
    -p 8081:8081 \
    --restart unless-stopped \
    hsm-websocket:latest

print_status "Waiting for container to be ready..."
sleep 5

# Health check
if curl -f http://localhost:8081/health > /dev/null 2>&1; then
    print_status "✅ Deployment successful! Server is running on http://localhost:8081"
    print_status "Health check: http://localhost:8081/health"
else
    print_error "❌ Deployment failed! Container might not be healthy."
    print_warning "Check container logs with: docker logs hsm-websocket-container"
    exit 1
fi

print_status "🎉 Deployment completed successfully!"
echo ""
echo "Useful commands:"
echo "  View logs: docker logs -f hsm-websocket-container"
echo "  Stop container: docker stop hsm-websocket-container"
echo "  Remove container: docker rm hsm-websocket-container"
