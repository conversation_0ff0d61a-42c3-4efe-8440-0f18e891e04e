version: '3.8'

services:
  hsm-websocket:
    build: .
    ports:
      - "8081:8081"
    environment:
      - NODE_ENV=production
    volumes:
      # Mount production env file if you want to override the copied one
      # - ./.env.production:/app/.env
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:8081/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - hsm-network

  # Optional: PostgreSQL database service (if you want to run DB in Docker too)
  # postgres:
  #   image: postgres:15-alpine
  #   environment:
  #     POSTGRES_DB: hsm_websocket
  #     POSTGRES_USER: postgres
  #     POSTGRES_PASSWORD: your_password
  #   volumes:
  #     - postgres_data:/var/lib/postgresql/data
  #   ports:
  #     - "5432:5432"
  #   networks:
  #     - hsm-network

networks:
  hsm-network:
    driver: bridge

# volumes:
#   postgres_data:
