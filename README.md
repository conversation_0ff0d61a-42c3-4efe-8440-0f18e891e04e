# HSM WebSocket Server

A TypeScript-based WebSocket server using Socket.IO for real-time communication.

## 🚀 Features

- **TypeScript**: Full TypeScript support with type safety
- **Socket.IO**: Real-time bidirectional event-based communication
- **Express**: Static file serving for client testing
- **Development Tools**: Hot reload with nodemon and ts-node

## 📦 Installation

```bash
npm install
```

## 🛠️ Development

### Start the development server with hot reload:
```bash
npm run dev
```

### Watch mode (compile TypeScript in watch mode):
```bash
npm run watch
```

### Development with nodemon (auto-restart on file changes):
```bash
npm run dev:watch
```

## 🏗️ Production

### Build the project:
```bash
npm run build
```

### Start the production server:
```bash
npm start
```

## 🧪 Testing

1. Start the development server:
   ```bash
   npm run dev
   ```

2. Open your browser and navigate to:
   ```
   http://localhost:3000
   ```

3. You should see a Socket.IO test client where you can:
   - See connection status
   - Send messages to the server
   - Receive responses from the server

## 📁 Project Structure

```
hsm-websocket/
├── src/
│   └── index.ts          # Main server file
├── public/
│   └── index.html        # Test client
├── dist/                 # Compiled JavaScript (after build)
├── package.json          # Dependencies and scripts
├── tsconfig.json         # TypeScript configuration
├── nodemon.json          # Nodemon configuration
└── README.md             # This file
```

## 📡 Socket.IO Events

### Client to Server:
- `message`: Send a message to the server

### Server to Client:
- `response`: Receive a response from the server

## 🎯 Usage Example

```typescript
// Client-side
socket.emit('message', 'Hello Server!');

socket.on('response', (data) => {
    console.log('Server responded:', data);
});
```

## � Configuration

### Environment Variables

- `PORT`: Server port (default: 3000)

### TypeScript Configuration

The project uses strict TypeScript settings with:
- ES2022 target
- Source maps enabled
- Declaration files generated
- Strict type checking

## �🚀 Available Scripts

| Script | Description |
|--------|-------------|
| `npm run dev` | Start development server with ts-node |
| `npm run dev:watch` | Start development server with auto-restart |
| `npm run build` | Compile TypeScript to JavaScript |
| `npm run start` | Start production server |
| `npm run watch` | Compile TypeScript in watch mode |

## 🔗 Dependencies

### Production:
- `socket.io`: WebSocket library
- `express`: Web framework for static files

### Development:
- `typescript`: TypeScript compiler
- `ts-node`: TypeScript execution engine
- `nodemon`: File watcher for auto-restart
- `@types/node`: Node.js type definitions
- `@types/express`: Express type definitions

## 📝 License

ISC
# hia-sang-ma-socket
