import { QueryResult } from 'pg';
import { db } from '../database';
import { Cha<PERSON>, ChatUser, UserWithChatDetails, ChatType } from '../types/chat';

/**
 * Get a chat by its ID
 */
export async function getChatById(chatId: number): Promise<Chat | null> {
  const query = `
    SELECT 
      id,
      name,
      chat_type as "chatType",
      organization_id as "organizationId",
      department_id as "departmentId",
      task_id as "taskId",
      is_active as "isActive",
      last_message_at as "lastMessageAt",
      created_at as "createdAt",
      updated_at as "updatedAt"
    FROM chats
    WHERE id = $1
  `;

  try {
    const result: QueryResult = await db.query(query, [chatId]);

    if (result.rows.length === 0) {
      return null;
    }

    const row = result.rows[0];
    return {
      id: row.id,
      name: row.name,
      chatType: row.chatType as ChatType,
      organizationId: row.organizationId,
      departmentId: row.departmentId,
      taskId: row.taskId,
      isActive: row.isActive,
      lastMessageAt: row.lastMessageAt,
      createdAt: row.createdAt,
      updatedAt: row.updatedAt
    };
  } catch (error) {
    console.error('Error fetching chat by ID:', error);
    throw new Error(`Failed to fetch chat with ID ${chatId}`);
  }
}

/**
 * Get users associated with a specific chat ID
 */
export async function getUsersWithChatId(chatId: number): Promise<UserWithChatDetails[]> {
  const query = `
    SELECT 
      cu.id,
      cu.chat_id as "chatId",
      cu.user_id as "userId",
      cu.is_admin as "isAdmin",
      cu.joined_at as "joinedAt",
      u.email,
      u.phone,
      u.first_name as "firstName",
      u.last_name as "lastName",
      u.image_url as "imageUrl"
    FROM chat_users cu
    INNER JOIN users u ON cu.user_id = u.id
    WHERE cu.chat_id = $1
    ORDER BY cu.joined_at ASC
  `;

  try {
    const result: QueryResult = await db.query(query, [chatId]);

    return result.rows.map(row => ({
      id: row.id,
      chatId: row.chatId,
      userId: row.userId,
      isAdmin: row.isAdmin,
      joinedAt: row.joinedAt,
      email: row.email,
      phone: row.phone,
      firstName: row.firstName,
      lastName: row.lastName,
      imageUrl: row.imageUrl
    }));
  } catch (error) {
    console.error('Error fetching users with chat ID:', error);
    throw new Error(`Failed to fetch users for chat with ID ${chatId}`);
  }
}
