import { QueryResult } from 'pg';
import { db } from '../database';
import { User } from '../types/user';
import { Department } from '../types/department';

/**
 * Get user by ID
 */
export async function getUserById(id: number): Promise<User | null> {
  const query = `
    SELECT 
      id,
      email,
      phone,
      first_name as "firstName",
      last_name as "lastName",
      image_url as "imageUrl",
      created_at as "createdAt",
      updated_at as "updatedAt"
    FROM users
    WHERE id = $1
  `;

  try {
    const result: QueryResult = await db.query(query, [id]);

    if (result.rows.length === 0) {
      return null;
    }

    return result.rows[0] as User;
  } catch (error) {
    console.error('Error getting user by ID:', error);
    throw new Error(`Failed to get user with ID: ${id}`);
  }
}

/**
 * Get departments for a specific user
 */
export async function getUserDepartments(userId: number): Promise<Department[]> {
  const query = `
    SELECT 
      d.id,
      d.name,
      d.description,
      d.organization_id as "organizationId",
      d.created_at as "createdAt",
      d.updated_at as "updatedAt"
    FROM departments d
    INNER JOIN department_members dm ON d.id = dm.department_id
    WHERE dm.user_id = $1
    ORDER BY d.name
  `;

  try {
    const result: QueryResult = await db.query(query, [userId]);
    return result.rows as Department[];
  } catch (error) {
    console.error('Error getting user departments:', error);
    throw new Error(`Failed to get departments for user with ID: ${userId}`);
  }
}

/**
 * Get user by ID with their departments
 */
export async function getUserWithDepartments(id: number): Promise<(User & { departments: Department[] }) | null> {
  try {
    const user = await getUserById(id);
    if (!user) {
      return null;
    }

    const departments = await getUserDepartments(id);
    
    return {
      ...user,
      departments
    };
  } catch (error) {
    console.error('Error getting user with departments:', error);
    throw new Error(`Failed to get user with departments for ID: ${id}`);
  }
}
