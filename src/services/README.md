# ChatMessage Service

A comprehensive service for managing chat messages with create, read, and update operations.

## Features

- ✅ Create new chat messages
- ✅ Read messages by ID, chat ID, or user ID
- ✅ Update message content, type, and status
- ✅ Bulk operations with filtering and pagination
- ✅ Status management (sending → delivered → read)
- ✅ TypeScript support with full type safety
- ✅ Error handling and logging
- ✅ Database migration support

## Database Schema

The service is based on this Prisma model:

```prisma
model ChatMessage {
  id            Int           @id @default(autoincrement())
  chatId        Int           @map("chat_id")
  userId        Int           @map("user_id")
  content       String        @db.Text
  messageType   MessageType   @default(TEXT) @map("message_type")
  messageStatus MessageStatus @default(SENDING) @map("message_status")
  createdAt     DateTime      @default(now()) @map("created_at")
  updatedAt     DateTime      @updatedAt @map("updated_at")

  @@map("chat_messages")
}

enum MessageType {
  TEXT    @map("text")
  IMAGE   @map("image")
  FILE    @map("file")
  STICKER @map("sticker")
}

enum MessageStatus {
  SENDING   @map("sending")
  DELIVERED @map("delivered")
  READ      @map("read")
  FAILED    @map("failed")
}
```

## Setup

### 1. Database Migration

Run the migration to create the necessary tables:

```typescript
import { setupChatMessagesTable } from './src/database';

// Setup the table (will create if not exists)
await setupChatMessagesTable();
```

### 2. Import the Service

```typescript
import { ChatMessageService } from './src/services';
import { MessageType, MessageStatus } from './src/types';
```

## Usage

### Creating Messages

```typescript
// Create a simple text message
const message = await ChatMessageService.create({
  chatId: 1,
  userId: 123,
  content: 'Hello, world!'
});

// Create with specific type and status
const imageMessage = await ChatMessageService.create({
  chatId: 1,
  userId: 123,
  content: 'https://example.com/image.jpg',
  messageType: MessageType.IMAGE,
  messageStatus: MessageStatus.DELIVERED
});
```

### Reading Messages

```typescript
// Get a single message by ID
const message = await ChatMessageService.getById(1);

// Get all messages in a chat
const chatMessages = await ChatMessageService.getByChatId(1);

// Get messages with pagination
const recentMessages = await ChatMessageService.getByChatId(1, 20, 0);

// Get messages by user
const userMessages = await ChatMessageService.getByUserId(123);

// Get messages with complex filtering
const filteredMessages = await ChatMessageService.getMany({
  chatId: 1,
  messageType: MessageType.TEXT,
  messageStatus: MessageStatus.READ,
  limit: 10,
  offset: 20
});
```

### Updating Messages

```typescript
// Update message content
const updated = await ChatMessageService.update(messageId, {
  content: 'Updated content',
  messageStatus: MessageStatus.DELIVERED
});

// Quick status updates
await ChatMessageService.markAsDelivered(messageId);
await ChatMessageService.markAsRead(messageId);
await ChatMessageService.markAsFailed(messageId);

// Update specific status
await ChatMessageService.updateStatus(messageId, MessageStatus.READ);
```

### Utility Operations

```typescript
// Count messages
const totalMessages = await ChatMessageService.count({ chatId: 1 });

// Count with filters
const unreadCount = await ChatMessageService.count({
  chatId: 1,
  messageStatus: MessageStatus.DELIVERED
});
```

## API Reference

### ChatMessageService Methods

#### `create(data: CreateChatMessageData): Promise<ChatMessage>`
Creates a new chat message.

**Parameters:**
- `data.chatId` (number): ID of the chat/room
- `data.userId` (number): ID of the user sending the message
- `data.content` (string): Message content
- `data.messageType` (MessageType, optional): Type of message (default: TEXT)
- `data.messageStatus` (MessageStatus, optional): Initial status (default: SENDING)

#### `getById(id: number): Promise<ChatMessage | null>`
Retrieves a message by its ID.

#### `getByChatId(chatId: number, limit?: number, offset?: number): Promise<ChatMessage[]>`
Gets all messages for a specific chat with pagination.

#### `getByUserId(userId: number, limit?: number, offset?: number): Promise<ChatMessage[]>`
Gets all messages sent by a specific user.

#### `getMany(filters: ChatMessageFilters): Promise<ChatMessage[]>`
Gets messages with complex filtering options.

#### `update(id: number, data: UpdateChatMessageData): Promise<ChatMessage | null>`
Updates a message's content, type, or status.

#### `updateStatus(id: number, status: MessageStatus): Promise<ChatMessage | null>`
Updates only the message status.

#### `markAsDelivered(id: number): Promise<ChatMessage | null>`
Quick method to mark a message as delivered.

#### `markAsRead(id: number): Promise<ChatMessage | null>`
Quick method to mark a message as read.

#### `markAsFailed(id: number): Promise<ChatMessage | null>`
Quick method to mark a message as failed.

#### `count(filters: ChatMessageFilters): Promise<number>`
Counts messages matching the given filters.

## Types

### Enums

```typescript
enum MessageType {
  TEXT = 'text',
  IMAGE = 'image',
  FILE = 'file',
  STICKER = 'sticker'
}

enum MessageStatus {
  SENDING = 'sending',
  DELIVERED = 'delivered',
  READ = 'read',
  FAILED = 'failed'
}
```

### Interfaces

```typescript
interface ChatMessage {
  id: number;
  chatId: number;
  userId: number;
  content: string;
  messageType: MessageType;
  messageStatus: MessageStatus;
  createdAt: Date;
  updatedAt: Date;
}

interface CreateChatMessageData {
  chatId: number;
  userId: number;
  content: string;
  messageType?: MessageType;
  messageStatus?: MessageStatus;
}

interface UpdateChatMessageData {
  content?: string;
  messageType?: MessageType;
  messageStatus?: MessageStatus;
}

interface ChatMessageFilters {
  chatId?: number;
  userId?: number;
  messageType?: MessageType;
  messageStatus?: MessageStatus;
  limit?: number;
  offset?: number;
}
```

## Error Handling

All methods include comprehensive error handling:

```typescript
try {
  const message = await ChatMessageService.create({
    chatId: 1,
    userId: 123,
    content: 'Hello!'
  });
} catch (error) {
  console.error('Failed to create message:', error.message);
}
```

## Message Flow

Typical message lifecycle:

1. **SENDING** → Message created by sender
2. **DELIVERED** → Message received by recipient's device
3. **READ** → Message viewed by recipient
4. **FAILED** → Message failed to send/deliver

## Performance Notes

- All queries include proper indexing for optimal performance
- Pagination is implemented for large message lists
- Composite indexes optimize common query patterns
- Prepared statements prevent SQL injection

## Database Indexes

The migration creates these indexes for optimal performance:

- `idx_chat_messages_chat_id` - Fast chat message lookups
- `idx_chat_messages_user_id` - Fast user message lookups
- `idx_chat_messages_created_at` - Chronological ordering
- `idx_chat_messages_status` - Status filtering
- `idx_chat_messages_type` - Type filtering
- `idx_chat_messages_chat_created` - Composite for chat + time queries
