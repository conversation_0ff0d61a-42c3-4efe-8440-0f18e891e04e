import { QueryResult } from 'pg';
import { db } from '../database';
import { 
  ChatMessage, 
  CreateChatMessageData, 
  UpdateChatMessageData, 
  ChatMessageFilters,
  MessageType,
  MessageStatus 
} from '../types/chatMessage';

/**
 * Create a new chat message
 */
export async function createChatMessage(data: CreateChatMessageData): Promise<ChatMessage> {
  const { 
    chatId, 
    userId, 
    content, 
    messageType = "text", 
    messageStatus = "sending" 
  } = data;

  const query = `
    INSERT INTO chat_messages (chat_id, user_id, content, message_type, message_status, created_at, updated_at)
    VALUES ($1, $2, $3, $4, $5, NOW(), NOW())
    RETURNING 
      id,
      chat_id as "chatId",
      user_id as "userId",
      content,
      message_type as "messageType",
      message_status as "messageStatus",
      created_at as "createdAt",
      updated_at as "updatedAt"
  `;

  try {
    const result: QueryResult = await db.query(query, [
      chatId,
      userId,
      content,
      messageType,
      messageStatus
    ]);

    if (result.rows.length === 0) {
      throw new Error('Failed to create chat message');
    }

    return result.rows[0] as ChatMessage;
  } catch (error) {
    console.error('❌ Error creating chat message:', error);
    throw new Error(`Failed to create chat message: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Get a chat message by ID
 */
export async function getChatMessageById(id: number): Promise<ChatMessage | null> {
  const query = `
    SELECT 
      id,
      chat_id as "chatId",
      user_id as "userId",
      content,
      message_type as "messageType",
      message_status as "messageStatus",
      created_at as "createdAt",
      updated_at as "updatedAt"
    FROM chat_messages 
    WHERE id = $1
  `;

  try {
    const result: QueryResult = await db.query(query, [id]);
    
    if (result.rows.length === 0) {
      return null;
    }

    return result.rows[0] as ChatMessage;
  } catch (error) {
    console.error('❌ Error fetching chat message by ID:', error);
    throw new Error(`Failed to fetch chat message: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Get multiple chat messages with filters
 */
export async function getManyChatMessages(filters: ChatMessageFilters = {}): Promise<ChatMessage[]> {
  const {
    chatId,
    userId,
    messageType,
    messageStatus,
    limit = 50,
    offset = 0
  } = filters;

  let query = `
    SELECT 
      id,
      chat_id as "chatId",
      user_id as "userId",
      content,
      message_type as "messageType",
      message_status as "messageStatus",
      created_at as "createdAt",
      updated_at as "updatedAt"
    FROM chat_messages
  `;

  const conditions: string[] = [];
  const params: any[] = [];
  let paramIndex = 1;

  if (chatId !== undefined) {
    conditions.push(`chat_id = $${paramIndex++}`);
    params.push(chatId);
  }

  if (userId !== undefined) {
    conditions.push(`user_id = $${paramIndex++}`);
    params.push(userId);
  }

  if (messageType !== undefined) {
    conditions.push(`message_type = $${paramIndex++}`);
    params.push(messageType);
  }

  if (messageStatus !== undefined) {
    conditions.push(`message_status = $${paramIndex++}`);
    params.push(messageStatus);
  }

  if (conditions.length > 0) {
    query += ` WHERE ${conditions.join(' AND ')}`;
  }

  query += ` ORDER BY created_at DESC LIMIT $${paramIndex++} OFFSET $${paramIndex++}`;
  params.push(limit, offset);

  try {
    const result: QueryResult = await db.query(query, params);
    return result.rows as ChatMessage[];
  } catch (error) {
    console.error('❌ Error fetching chat messages:', error);
    throw new Error(`Failed to fetch chat messages: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Get chat messages by chat ID (most common use case)
 */
export async function getChatMessagesByChatId(chatId: number, limit: number = 50, offset: number = 0): Promise<ChatMessage[]> {
  return getManyChatMessages({ chatId, limit, offset });
}

/**
 * Get chat messages by user ID
 */
export async function getChatMessagesByUserId(userId: number, limit: number = 50, offset: number = 0): Promise<ChatMessage[]> {
  return getManyChatMessages({ userId, limit, offset });
}

/**
 * Update a chat message
 */
export async function updateChatMessage(id: number, data: UpdateChatMessageData): Promise<ChatMessage | null> {
  const { content, messageType, messageStatus } = data;

  const updates: string[] = [];
  const params: any[] = [];
  let paramIndex = 1;

  if (content !== undefined) {
    updates.push(`content = $${paramIndex++}`);
    params.push(content);
  }

  if (messageType !== undefined) {
    updates.push(`message_type = $${paramIndex++}`);
    params.push(messageType);
  }

  if (messageStatus !== undefined) {
    updates.push(`message_status = $${paramIndex++}`);
    params.push(messageStatus);
  }

  if (updates.length === 0) {
    throw new Error('No fields to update');
  }

  updates.push(`updated_at = NOW()`);
  params.push(id);

  const query = `
    UPDATE chat_messages 
    SET ${updates.join(', ')}
    WHERE id = $${paramIndex}
    RETURNING 
      id,
      chat_id as "chatId",
      user_id as "userId",
      content,
      message_type as "messageType",
      message_status as "messageStatus",
      created_at as "createdAt",
      updated_at as "updatedAt"
  `;

  try {
    const result: QueryResult = await db.query(query, params);
    
    if (result.rows.length === 0) {
      return null;
    }

    return result.rows[0] as ChatMessage;
  } catch (error) {
    console.error('❌ Error updating chat message:', error);
    throw new Error(`Failed to update chat message: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Update message status (common operation)
 */
export async function updateChatMessageStatus(id: number, messageStatus: MessageStatus): Promise<ChatMessage | null> {
  return updateChatMessage(id, { messageStatus });
}

/**
 * Mark message as delivered
 */
export async function markChatMessageAsDelivered(id: number): Promise<ChatMessage | null> {
  return updateChatMessageStatus(id, "delivered");
}

/**
 * Mark message as read
 */
export async function markChatMessageAsRead(id: number): Promise<ChatMessage | null> {
  return updateChatMessageStatus(id, "read");
}

/**
 * Mark message as failed
 */
export async function markChatMessageAsFailed(id: number): Promise<ChatMessage | null> {
  return updateChatMessageStatus(id, "failed");
}

/**
 * Count total messages with filters
 */
export async function countChatMessages(filters: Omit<ChatMessageFilters, 'limit' | 'offset'> = {}): Promise<number> {
  const {
    chatId,
    userId,
    messageType,
    messageStatus
  } = filters;

  let query = 'SELECT COUNT(*) as count FROM chat_messages';
  const conditions: string[] = [];
  const params: any[] = [];
  let paramIndex = 1;

  if (chatId !== undefined) {
    conditions.push(`chat_id = $${paramIndex++}`);
    params.push(chatId);
  }

  if (userId !== undefined) {
    conditions.push(`user_id = $${paramIndex++}`);
    params.push(userId);
  }

  if (messageType !== undefined) {
    conditions.push(`message_type = $${paramIndex++}`);
    params.push(messageType);
  }

  if (messageStatus !== undefined) {
    conditions.push(`message_status = $${paramIndex++}`);
    params.push(messageStatus);
  }

  if (conditions.length > 0) {
    query += ` WHERE ${conditions.join(' AND ')}`;
  }

  try {
    const result: QueryResult = await db.query(query, params);
    return parseInt(result.rows[0].count, 10);
  } catch (error) {
    console.error('❌ Error counting chat messages:', error);
    throw new Error(`Failed to count chat messages: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}
