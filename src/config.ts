import dotenv from 'dotenv';

// Load environment variables from .env file
dotenv.config();

// Configuration interface
interface Config {
  port: number;
  appName: string;
  nodeEnv: string;
  cors: {
    origin: string;
    methods: string[];
  };
  database: {
    host: string;
    port: number;
    database: string;
    username: string;
    password: string;
    ssl: boolean;
  };
}

// Create configuration object
const config: Config = {
  port: Number(process.env.PORT) || 3000,
  appName: process.env.APP_NAME || 'WebSocket Server',
  nodeEnv: process.env.NODE_ENV || 'development',
  cors: {
    origin: process.env.SOCKET_CORS_ORIGIN || '*',
    methods: (process.env.SOCKET_CORS_METHODS || 'GET,POST').split(',')
  },
  database: {
    host: process.env.DB_HOST || 'localhost',
    port: Number(process.env.DB_PORT) || 5432,
    database: process.env.DB_NAME || 'hsm_websocket',
    username: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || '',
    ssl: process.env.DB_SSL === 'true'
  }
};

// Function to log configuration
export const logConfig = (): void => {
  console.log('🔧 Environment Configuration:');
  console.log(`   NODE_ENV: ${config.nodeEnv}`);
  console.log(`   PORT: ${config.port}`);
  console.log(`   APP_NAME: ${config.appName}`);
  console.log(`   CORS Origin: ${config.cors.origin}`);
  console.log(`   DB Host: ${config.database.host}:${config.database.port}`);
  console.log(`   DB Name: ${config.database.database}`);
  console.log(`   DB User: ${config.database.username}`);
  console.log('');
};

export default config;
