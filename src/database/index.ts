import { Pool, QueryResult } from "pg";
import config from "../config";


// Create a single pool instance
const pool = new Pool({
  host: config.database.host,
  port: config.database.port,
  database: config.database.database,
  user: config.database.username,
  password: config.database.password,
  ssl: config.database.ssl ? { rejectUnauthorized: false } : false,
  max: 20, // Maximum number of clients in the pool
  idleTimeoutMillis: 30000, // Close idle clients after 30 seconds
  connectionTimeoutMillis: 2000, // Return an error after 2 seconds if connection could not be established
});

// Handle pool errors
pool.on("error", (err) => {
  console.error("❌ Unexpected error on idle client", err);
  process.exit(-1);
});

/**
 * Execute a query with optional parameters
 */
export const query = async (
  text: string,
  params?: any[]
): Promise<QueryResult> => {
  // const start = Date.now();
  try {
    const result = await pool.query(text, params);
    // const duration = Date.now() - start;
    // console.log("📊 Query executed:", {
    //   text,
    //   duration,
    //   rows: result.rowCount,
    // });
    return result;
  } catch (error) {
    console.error("❌ Database query error:", error);
    throw error;
  }
};

/**
 * Test the database connection
 */
export const testConnection = async (): Promise<boolean> => {
  try {
    const result = await query("SELECT NOW()");
    console.log("✅ Database connection successful at:", result.rows[0].now);
    return true;
  } catch (error) {
    console.error("❌ Database connection failed:", error);
    return false;
  }
};

/**
 * Close all connections in the pool
 */
export const close = async (): Promise<void> => {
  await pool.end();
  console.log("🔌 Database connection pool closed");
};

/**
 * Get pool status
 */
export const getPoolStatus = () => {
  return {
    totalCount: pool.totalCount,
    idleCount: pool.idleCount,
    waitingCount: pool.waitingCount,
  };
};

// Export database functions as an object for convenience
export const db = {
  query,
  testConnection,
  close,
  getPoolStatus,
};