# Database Connection Setup

This project includes PostgreSQL database connectivity using the `pg` library. The database connection is configured as a singleton pattern for efficient connection pooling.

## Setup

### 1. Install PostgreSQL
Make sure you have PostgreSQL installed and running on your system.

### 2. Environment Variables
Copy `.env.example` to `.env` and configure your database settings:

```bash
cp .env.example .env
```

Edit `.env` file:
```bash
# Database Configuration (PostgreSQL)
DB_HOST=localhost
DB_PORT=5432
DB_NAME=hsm_websocket
DB_USER=postgres
DB_PASSWORD=your_password_here
DB_SSL=false
```

### 3. Create Database
Create the database in PostgreSQL:

```sql
CREATE DATABASE hsm_websocket;
```

## Usage

### Basic Database Connection

The database connection provides individual functions that you can import:

```typescript
import { query, testConnection, close, getPoolStatus } from './database/database';

// Simple query
const result = await query('SELECT NOW()');
console.log(result.rows[0]);

// Query with parameters
const user = await query('SELECT * FROM users WHERE id = $1', [userId]);
```

You can also import the `db` object that contains all functions:

```typescript
import { db } from './database/database';

// Using the db object
const result = await db.query('SELECT NOW()');
await db.testConnection();
```

### Connection Management

The database connection includes:

- **Connection pooling** (max 20 connections)
- **Automatic error handling**
- **Connection testing**
- **Graceful shutdown**

### Available Functions

```typescript
// Execute a query
await query(sql, params);

// Test connection
await testConnection();

// Close all connections
await close();

// Get pool status
getPoolStatus();
```

## Examples

Check `src/examples/databaseExamples.ts` for detailed usage examples including:

- Simple SELECT queries
- INSERT with RETURNING
- UPDATE operations
- DELETE operations
- Complex queries with JOINs
- Parameterized queries

## Error Handling

The database connection includes automatic error handling and logging. All queries are logged with execution time and row count for debugging purposes.

## Connection Pool

The connection pool is configured with:
- Maximum 20 connections
- 30-second idle timeout
- 2-second connection timeout

## Notes

- The database connection is initialized automatically when the application starts
- Connection testing is performed during startup
- Graceful shutdown is handled automatically
- All queries use parameterized statements to prevent SQL injection
