export enum ChatType {
  PRIVATE = 'PRIVATE',
  TASK = 'TASK',
  ORGANIZATION = 'ORGANIZATION',
  DEPARTMENT = 'DEPARTMENT',
}

export interface Chat {
  id: number;
  name?: string;
  chatType: ChatType;
  organizationId?: number;
  departmentId?: number;
  taskId?: number;
  isActive: boolean;
  lastMessageAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface ChatUser {
  id: number;
  chatId: number;
  userId: number;
  isAdmin: boolean;
  joinedAt: Date;
}

export interface UserWithChatDetails {
  id: number;
  chatId: number;
  userId: number;
  isAdmin: boolean;
  joinedAt: Date;
  // User details
  email: string;
  phone?: string;
  firstName?: string;
  lastName?: string;
  imageUrl?: string;
}
