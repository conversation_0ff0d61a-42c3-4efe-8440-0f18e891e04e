import express from 'express';
import { createServer } from 'http';
import path from 'path';

// Create Express app and HTTP server
const app = express();
const httpServer = createServer(app);

// Health check endpoint for Docker
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

// Serve static files from public directory
app.use(express.static(path.join(__dirname, '../public')));

export { app, httpServer };
