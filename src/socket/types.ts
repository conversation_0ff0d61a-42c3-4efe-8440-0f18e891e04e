import { MessageType, MessageStatus } from "../types";

// Define interfaces for Socket.IO type safety
export interface User {
  id: number;
  email: string;
  phone?: string;
  firstName: string;
  lastName: string;
  imageUrl?: string | null;
  departmentIds?: number[];
  organizationIds?: number[];
}
export type ChatType = "private" | "task" | "department" | "organization";

export interface Chat {
  chatId: number;
  lastMessage?: Message | null;
  timestamp: Date;
  type: ChatType;
  participants: User[];
}

export interface Message {
  messageId: number;
  content: string;
  sender: User;
  createdAt: any;
  messageType?: MessageType;
  status: MessageStatus;
}

export interface SendMessage {
  chatId: number;
  content: string;
  sender: User;
  messageType?: MessageType;
  timestamp?: Date;
}

export interface ServerToClientEvents {
  connected: (message: string) => void;
  receive_join: (data: { message: string; user: User }) => void;
  receive_leave: (data: { message: string; user: User }) => void;

  chat_notification: (data: {
    message: string;
    data: {
      user: User;
      message: string;
    };
  }) => void;
  new_message: (message: Message) => void;
  // message_sent: (message: Message) => void;
  // message_status: (message: Message) => void;
}

export interface ClientToServerEvents {
  join_chat: (message: { chat_id: number }) => void;
  leave_chat: (message: { chat_id: number }) => void;

  send_message: (send: SendMessage) => void;
  message_read: (messageId: number) => void;

  send_notification: () => void;
}

export interface InterServerEvents {
  ping: () => void;
}

export interface SocketData {
  user: User;
  isOnline?: boolean;
}
