import { Server } from 'socket.io';
import { httpServer } from '../httpServer';
import config from '../config';
import { setupSocketHandlers } from './handlers';
import {
  ClientToServerEvents,
  ServerToClientEvents,
  InterServerEvents,
  SocketData
} from './types';

// Create Socket.IO server with types
export const io = new Server<
  ClientToServerEvents,
  ServerToClientEvents,
  InterServerEvents,
  SocketData
>(httpServer, {
  cors: {
    origin: config.cors.origin,
    methods: config.cors.methods
  }
});

// Initialize socket event handlers
export const initializeSocket = () => {
  io.on('connection', setupSocketHandlers);
};
