import { Socket } from "socket.io";
import {
  ClientToServerEvents,
  ServerToClientEvents,
  SocketData,
} from "../types";
import { getUserById, getUserWithDepartments } from "../../services";
import { Department } from "../../types";

export const handleConnection = (
  socket: Socket<ClientToServerEvents, ServerToClientEvents, any, SocketData>
) => {
  const timestamp = new Date().toISOString();

  // Extract connection parameters from handshake (exclude Socket.IO internal params)
  const { query, auth } = socket.handshake;
  const allParams = { ...query, ...auth };

  // Filter out Socket.IO internal parameters
  const socketIOInternalParams = ["EIO", "transport", "t"];
  const connectionParams = Object.fromEntries(
    Object.entries(allParams).filter(
      ([key]) => !socketIOInternalParams.includes(key)
    )
  );

  // Extract specific parameters
  const userId = connectionParams.userId as string;

  // Initialize socket data if not exists
  if (!socket.data.user) {
    socket.data.user = {
      id: 0,
      email: "",
      phone: "",
      firstName: "",
      lastName: "",
      imageUrl: "",
      departmentIds: [],
      organizationIds: [],
    };
  }

  if (userId) {
    async function fetchUserData() {
      try {
        const user: any = await getUserById(Number(userId));
        const userWithDepartments: any = await getUserWithDepartments(
          Number(userId)
        );
        if (user) {
          // Extract departmentIds and organizationIds from departments
          const departments: Department[] =
            userWithDepartments.departments || [];
          const departmentIds: number[] = departments.map(
            (dept: Department) => dept.id
          );
          const organizationIdsSet = new Set<number>();
          departments.forEach((dept: Department) => {
            organizationIdsSet.add(dept.organizationId);
          });
          const organizationIds: number[] = Array.from(organizationIdsSet);

          console.log(`[${timestamp}] ✅ Department IDs:`, departmentIds);
          console.log(
            `[${timestamp}] ✅ Organization IDs (unique):`,
            organizationIds
          );

          socket.data.user = {
            id: user.id,
            email: user.email,
            firstName: user.firstName,
            lastName: user.lastName,
            imageUrl: user.imageUrl || "",
            phone: user.phone || "",
            departmentIds,
            organizationIds,
          };

          socket.join(`user_${user.id}`);
        

          // // Join department rooms
          // departmentIds.forEach((deptId: number) => {
          //   socket.join(`dept_${deptId}`);
          //   console.log(
          //     `[${timestamp}] ✅ User joined department room: dept_${deptId}`
          //   );
          // });

          // // Join organization rooms
          // organizationIds.forEach((orgId: number) => {
          //   socket.join(`organize_${orgId}`);
          //   console.log(
          //     `[${timestamp}] ✅ User joined organization room: organize_${orgId}`
          //   );
          // });

          socket.emit(
            "connected",
            `You have joined the global for user ${socket.data.user.firstName} ${socket.data.user.lastName}.`
          );
          console.log(
            `[${timestamp}] ✅ User ${socket.data.user.email} connected with socket ID: ${socket.id}`
          );
        }
      } catch (error) {
        console.error("Error fetching user data:", error);
      }
    }
    fetchUserData();
  } else {
    console.log(
      `[${timestamp}] ✅ Anonymous user connected with socket ID: ${socket.id}`
    );
  }
};

export const handleDisconnection = (
  socket: Socket<ClientToServerEvents, ServerToClientEvents, any, SocketData>
) => {
  socket.on("disconnect", () => {
    const timestamp = new Date().toISOString();
    const userId = socket.data.user?.id;
    // const departmentIds = socket.data.user?.departmentIds || [];
    // const organizationIds = socket.data.user?.organizationIds || [];

    if (userId) {
      socket.leave(`user_${userId}`);

      // // Leave department rooms
      // departmentIds.forEach((deptId: number) => {
      //   socket.leave(`dept_${deptId}`);
      //   console.log(
      //     `[${timestamp}] ❌ User left department room: dept_${deptId}`
      //   );
      // });

      // // Leave organization rooms
      // organizationIds.forEach((orgId: number) => {
      //   socket.leave(`organize_${orgId}`);
      //   console.log(
      //     `[${timestamp}] ❌ User left organization room: organize_${orgId}`
      //   );
      // });

      console.log(
        `[${timestamp}] ❌ User ${userId} disconnected and left room: ${socket.id}`
      );
    } else {
      console.log(
        `[${timestamp}] ❌ Anonymous user disconnected: ${socket.id}`
      );
    }
  });
};
