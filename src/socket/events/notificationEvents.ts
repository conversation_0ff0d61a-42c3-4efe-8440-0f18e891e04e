import { Socket } from "socket.io";
import {
  ClientToServerEvents,
  ServerToClientEvents,
  SocketData,
} from "../types";

export const handlePushNotification = (
  socket: Socket<ClientToServerEvents, ServerToClientEvents, any, SocketData>
) => {
  socket.on("send_notification", () => {
    try {
      console.log(
        `[${new Date().toISOString()}] 💬 Notification sent to user ${
          socket.data.user.id
        }:`,
        JSON.stringify({ type: "global", data: "trigger" }, null, 2)
      );
    } catch (error) {
      console.error(
        `[${new Date().toISOString()}] ❌ Error sending notification:`,
        error
      );
    }
  });
};
