import { Socket } from "socket.io";
import {
  ClientToServerEvents,
  ServerToClientEvents,
  SocketData,
} from "../types";
// import { getUsersWithChatId } from "../../services";

export const handleChatRoom = (
  socket: Socket<ClientToServerEvents, ServerToClientEvents, any, SocketData>
) => {
  socket.on("join_chat", (message: { chat_id: number }) => {
    const currentRooms = Array.from(socket.rooms);
    currentRooms.forEach((room: string) => {
      if (room !== `user_${socket.data.user.id}`) {
        socket.leave(room);
        socket.to(room).emit("receive_leave", {
          message: `User left chat room ${room}`,
          user: socket.data.user,
        });
      }
    });
    try {
      socket.join(`chat_${message.chat_id}`);
      // socket.emit("receive_join", {
      //   message: `You have joined the chat ${message.chat_id}.`,
      //   user: socket.data.user,
      // });

      socket.to(`chat_${message.chat_id}`).emit("receive_join", {
        message: `New user joined chat : ${socket.data.user.firstName} ${socket.data.user.lastName}`,
        user: socket.data.user,
      });
      // async function notifyUsers() {
      //   try {
      //     const users = await getUsersWithChatId(message.chat_id);
      //     if (users.length > 0) {
      //       for (const user of users) {
      //         socket
      //           .to(`user_${user.userId}`)
      //           .emit(
      //             "receive_join",
      //             `New user joined chat ${message.chat_id}: ${socket.data.user.firstName} ${socket.data.user.lastName}`
      //           );
      //       }
      //     } else {
      //       console.log(
      //         `[${new Date().toISOString()}] ❌ No users found in chat ${
      //           message.chat_id
      //         }`
      //       );
      //     }
      //   } catch (error) {
      //     console.error(
      //       `[${new Date().toISOString()}] ❌ Error getting users:`,
      //       error
      //     );
      //   }
      // }
      // notifyUsers();
      console.log(
        `[${new Date().toISOString()}] ✅ User ${
          socket.data.user.id
        } joined chat: ${message.chat_id}`
      );
    } catch (error) {
      console.error(
        `[${new Date().toISOString()}] ❌ Error joining chat:`,
        error
      );
    }
  });

  socket.on("leave_chat", (message: { chat_id: number }) => {
    try {
      socket.leave(`chat_${message.chat_id}`);

      socket.to(`chat_${message.chat_id}`).emit("receive_leave", {
        message: `User left chat : ${socket.data.user.firstName} ${socket.data.user.lastName}`,
        user: socket.data.user,
      });
      console.log(
        `[${new Date().toISOString()}] 🚪 User ${
          socket.data.user.id
        } left chat: ${message.chat_id}`
      );
    } catch (error) {
      console.error(
        `[${new Date().toISOString()}] ❌ Error leaving chat:`,
        error
      );
    }
  });
};
