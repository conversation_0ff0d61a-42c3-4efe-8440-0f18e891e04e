import { httpServer } from './httpServer';
import config, { logConfig } from './config';
import { initializeSocket } from './socket';
import { testConnection, close } from './database/database';

// Initialize database connection and test it
async function initializeApp() {
    try {
        // Log environment variables (for debugging)
        logConfig();

        // Test database connection
        console.log('🔌 Testing database connection...');
        const isConnected = await testConnection();
        
        if (!isConnected) {
            console.error('❌ Failed to connect to database. Exiting...');
            process.exit(1);
        }

        // Initialize Socket.IO
        initializeSocket();

        // Start the server
        httpServer.listen(config.port, () => {
            console.log('🚀 =================================');
            console.log(`🚀 ${config.appName}`);
            console.log(`🚀 Running on: http://localhost:${config.port}`);
            console.log(`🚀 Environment: ${config.nodeEnv}`);
            console.log(`�️  Database: Connected to PostgreSQL`);
            console.log(`�📡 Socket.IO server ready for connections`);
            console.log('🚀 =================================');
        });

    } catch (error) {
        console.error('❌ Failed to initialize application:', error);
        process.exit(1);
    }
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
    console.log('\n🛑 Graceful shutdown initiated...');
    try {
        await close();
        console.log('🔌 Database connections closed');
        process.exit(0);
    } catch (error) {
        console.error('❌ Error during shutdown:', error);
        process.exit(1);
    }
});

process.on('SIGTERM', async () => {
    console.log('\n🛑 SIGTERM received, shutting down gracefully...');
    try {
        await close();
        console.log('🔌 Database connections closed');
        process.exit(0);
    } catch (error) {
        console.error('❌ Error during shutdown:', error);
        process.exit(1);
    }
});

// Start the application
initializeApp();