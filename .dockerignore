# Node modules (will be installed in container)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build outputs (will be built in container)
dist/
build/

# Development environment files (we're copying .env explicitly)
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory
coverage/
*.lcov

# nyc test coverage
.nyc_output

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Git
.git
.gitignore

# Docker files
Dockerfile*
.dockerignore

# Documentation
README.md
*.md

# Development tools
nodemon.json
