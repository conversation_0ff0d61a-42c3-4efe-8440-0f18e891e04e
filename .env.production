# Production Environment Configuration
PORT=8081
NODE_ENV=production

# Socket.IO Configuration
SOCKET_CORS_ORIGIN=https://yourdomain.com
SOCKET_CORS_METHODS=GET,POST

# Application Settings
APP_NAME=HSM WebSocket Server
LOG_LEVEL=warn

# Database Configuration (PostgreSQL)
# Replace these with your production database credentials
DB_HOST=your-production-db-host
DB_PORT=5432
DB_NAME=your-production-db-name
DB_USER=your-production-db-user
DB_PASSWORD=your-production-db-password
DB_SSL=true
